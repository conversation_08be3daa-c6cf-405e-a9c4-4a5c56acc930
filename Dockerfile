FROM registry.gdbyway.com/gdbyway/openjdk:8u212-jdk-alpine-arthas

ENV LOG_HOME="/opt/app/third-upload-job-executor/log/"

COPY ./target/third-upload-job-executor.jar /opt/app/third-upload-job-executor/web.jar

ENTRYPOINT ["/sbin/tini","--"]

CMD [ \
"sh", "-c", \
"java $JAVA_OPTS \
-Dfile.encoding=UTF-8 \
-DLOG_PATH=/opt/app/third-upload-job-executor/log \
-Djava.io.tmpdir=/tmp/ \
-Duser.timezone=Asia/Shanghai \
-Djava.library.path=/usr/local/lib/ \
-Djava.security.egd=file:/dev/./urandom \
-jar /opt/app/third-upload-job-executor/web.jar" \
]
