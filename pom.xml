<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.3.10.RELEASE</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <groupId>com.gdbyway.bpg</groupId>
  <artifactId>third-upload-job-executor</artifactId>
  <version>1.1.0-SNAPSHOT</version>
  <name>third-data-sync-job-executor</name>
  <description>数据推送第三方定时任务</description>
  <packaging>jar</packaging>

  <properties>
    <byway-job.version>2.0.2</byway-job.version>
    <mybatis-plus.version>3.5.1</mybatis-plus.version>
    <mssql-jdbc.version>7.3.0.jre8-preview</mssql-jdbc.version>
    <mysql.driver.version>5.1.47</mysql.driver.version>
    <ojdbc8.version>********</ojdbc8.version>
    <redisson.version>3.14.0</redisson.version>
    <vjtools.version>1.0.8</vjtools.version>
    <apollo.version>1.8.0</apollo.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- mysql数据库驱动 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql.driver.version}</version>
    </dependency>

    <!-- oracle数据库驱动 -->
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
      <version>${ojdbc8.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.oracle.ojdbc</groupId>
      <artifactId>orai18n</artifactId>
      <version>${ojdbc8.version}</version>
    </dependency>

    <!-- sql server数据库驱动 -->
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <version>${mssql-jdbc.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.28</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>21.0</version>
    </dependency>

    <dependency>
      <groupId>com.vip.vjtools</groupId>
      <artifactId>vjkit</artifactId>
      <version>${vjtools.version}</version>
    </dependency>

    <dependency>
      <groupId>com.ewell</groupId>
      <artifactId>byway-job-core</artifactId>
      <version>${byway-job.version}</version>
    </dependency>

    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>${redisson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.ctrip.framework.apollo</groupId>
      <artifactId>apollo-client</artifactId>
      <version>${apollo.version}</version>
    </dependency>

    <!-- Jackson XML 支持 -->
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
      <version>2.11.4</version>
    </dependency>

  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <generateBackupPoms>false</generateBackupPoms>
        </configuration>
      </plugin>
      <!-- 打源程序包 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>verify</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- 发布插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.4.RELEASE</version>
        <configuration>
          <mainClass>com.gdbyway.bpg.third.upload.boot.ThirdUploadJobExecutorApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
      </plugin>

    </plugins>
  </build>

  <distributionManagement>
    <snapshotRepository>
      <id>maven-snapshots</id>
      <name>maven-snapshots</name>
      <url>http://nexus.gdbyway.com/repository/maven-snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>maven-releases</id>
      <name>maven-releases</name>
      <url>http://nexus.gdbyway.com/repository/maven-releases/</url>
    </repository>
  </distributionManagement>

</project>
