package com.gdbyway.bpg.third.upload.boot;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <p>启动类</p>
 * <AUTHOR>
 * @date 2024/6/8
 */
@SpringBootApplication(scanBasePackages = "com.gdbyway.bpg.third.upload")
@MapperScan(basePackages = {
        "com.gdbyway.bpg.third.upload.mapper"
})
public class ThirdUploadJobExecutorApplication {

    public static void main(String[] args) {
        SpringApplication.run(ThirdUploadJobExecutorApplication.class, args);
    }

}
