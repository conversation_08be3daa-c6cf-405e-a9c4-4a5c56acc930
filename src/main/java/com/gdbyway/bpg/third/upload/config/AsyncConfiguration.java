package com.gdbyway.bpg.third.upload.config;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Copyright (C) 2024 广东百慧科技有限公司
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-05-19
 * <p>
 * Modification History:
 * Date             Author      Version     Description
 * -----------------------------------------------------------------
 * 2024-12-04    VincentHo       v1.0.0        create
 */
@Configuration
public class AsyncConfiguration implements AsyncConfigurer {

    private static final Log logger = LogFactory.getLog(AsyncConfiguration.class);

    private static final String THREAD_NAME_PREFIX = "async-thread-";

    /** 核心线程数 */
    @Value("${third.upload.async.thread-pool.core-pool-size:5}")
    private Integer corePoolSize;

    /** 最大线程数 */
    @Value("${third.upload.async.thread-pool.max-pool-size:100}")
    private Integer maxPoolSize;

    /** 队列大小 */
    @Value("${third.upload.async.thread-pool.queue-capacity:10}")
    private Integer queueCapacity;

    /** 线程最大空闲时间 */
    @Value("${third.upload.async.thread-pool.keep-alive-seconds:300}")
    private Integer keepAliveSeconds;

    @Bean("commonAsyncThreadPoolExecutor")
    public ThreadPoolTaskExecutor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);

        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        executor.initialize();
        return executor;
    }

    @Override
    public Executor getAsyncExecutor() {
        return executor();
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> logger.error(String.format("执行异步任务'%s'", method), ex);
    }
}