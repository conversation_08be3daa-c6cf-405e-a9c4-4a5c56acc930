package com.gdbyway.bpg.third.upload.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Copyright (C) 2019 Ewell
 *
 * @className:com.ewell.wsi.common.config.BywayJobConfig
 * @description: 定时任务相关配置
 * @author:vince
 * @Date:2020-03-25
 * @version:1.0.0
 * Date            Author      Version     Description
 * -----------------------------------------------------------------
 * 2020-03-25        vince       1.0.0        create
 */
@Configuration
public class BywayJobConfig {
    private Logger logger = LoggerFactory.getLogger(BywayJobConfig.class);

    @Value("${byway.job.admin.addresses}")
    private String adminAddresses;

    @Value("${byway.job.executor.appname}")
    private String appName;

    @Value("${byway.job.executor.ip}")
    private String ip;

    @Value("${byway.job.executor.port}")
    private int port;

    @Value("${byway.job.accessToken}")
    private String accessToken;

    @Value("${byway.job.executor.logpath}")
    private String logPath;

    @Value("${byway.job.executor.logretentiondays}")
    private int logRetentionDays;


    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobSpringExecutor xxlJobExecutor() {
        logger.info(">>>>>>>>>>> byway-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppName(appName);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

        return xxlJobSpringExecutor;
    }

    /**
     * 针对多网卡、容器内部署等情况，可借助 "spring-cloud-commons" 提供的 "InetUtils" 组件灵活定制注册IP；
     *
     *      1、引入依赖：
     *          <dependency>
     *             <groupId>org.springframework.cloud</groupId>
     *             <artifactId>spring-cloud-commons</artifactId>
     *             <version>${version}</version>
     *         </dependency>
     *
     *      2、配置文件，或者容器启动变量
     *          spring.cloud.inetutils.preferred-networks: 'xxx.xxx.xxx.'
     *
     *      3、获取IP
     *          String ip_ = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
     */
}

