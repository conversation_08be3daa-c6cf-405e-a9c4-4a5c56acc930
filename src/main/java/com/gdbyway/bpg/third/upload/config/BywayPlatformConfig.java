package com.gdbyway.bpg.third.upload.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>平台配置</p>
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "platform")
public class BywayPlatformConfig {

  private String transferUrl;

  private String tokenUrl;

  private String epKey;

}
