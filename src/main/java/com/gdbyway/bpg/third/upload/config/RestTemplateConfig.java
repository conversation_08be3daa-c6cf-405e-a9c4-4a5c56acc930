package com.gdbyway.bpg.third.upload.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>restTemplate设置</p>
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Component
public class RestTemplateConfig {

    @Value("${third.upload.rest-template.connection-timeout:3000}")
    private Integer connectionTimeout;

    @Value("${third.upload.rest-template.read-timeout:5000}")
    private Integer readTimeout;

    private static RestTemplateConfig INSTANCE;

    public RestTemplateConfig() {
        INSTANCE = this;
    }

    public static Integer getConnectionTimeout() {
        return INSTANCE.connectionTimeout;
    }

    public static Integer getReadTimeout() {
        return INSTANCE.readTimeout;
    }
}
