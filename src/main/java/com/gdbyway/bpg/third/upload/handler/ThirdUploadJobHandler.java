package com.gdbyway.bpg.third.upload.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.gdbyway.bpg.third.upload.constant.ThirdUploadConstant;
import com.gdbyway.bpg.third.upload.model.command.ThirdUploadJobCommand;
import com.gdbyway.bpg.third.upload.service.interfaces.AbstractThirdUploadJobService;
import com.gdbyway.bpg.third.upload.utils.JsonUtils;
import com.google.common.base.Throwables;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <p>上传定时任务处理器</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Component
@RequiredArgsConstructor
@JobHandler("thirdUploadJobHandler")
public class ThirdUploadJobHandler extends IJobHandler {

    private final Map<String, AbstractThirdUploadJobService> thirdUploadJobServiceMap;

    @Override
    public ReturnT<String> execute(String commandJson) throws Exception {

        try {
            // 解析定时任务入参
            ThirdUploadJobCommand command = commandJson2Obj(commandJson);
            XxlJobLogger.log("本次任务入参：{}", command);

            // 执行上传
            return getService(command).upload(command);
        } catch (Exception e) {
            if (e instanceof InterruptedException){
                XxlJobLogger.log("异常或手动终止任务！");
                throw e;
            }
            throw e;
        }

    }

    /**
     * <p>根据入参获取处理service</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param command
     * @return com.gdbyway.bpg.third.upload.service.interfaces.IThirdUploadJobService
     */
    private AbstractThirdUploadJobService getService(ThirdUploadJobCommand command) {
        if(command.getIsRetryFailRecord() != null || !CollectionUtils.isEmpty(command.getBizIdArray())) {
            return thirdUploadJobServiceMap.get(ThirdUploadConstant.MANUAL_THIRD_UPLOAD_SERVICE_NAME);
        } else {
            return thirdUploadJobServiceMap.get(ThirdUploadConstant.AUTO_THIRD_UPLOAD_SERVICE_NAME);
        }
    }

    /**
     * <p>入参json转换对象</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param commandJson
     * @return com.gdbyway.bpg.third.upload.model.command.UploadJobCommand
     */
    private ThirdUploadJobCommand commandJson2Obj(String commandJson) {
        try {
            return JsonUtils.readValue(commandJson, ThirdUploadJobCommand.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("任务入参配置异常，解析失败，请检查入参配置是否符合规范。" +
                    "详情查看以下wiki内容：" +
                    "http://wiki.gdbyway.com/pages/viewpage.action?pageId=156272259\n" +
                    "报错详情：" + Throwables.getStackTraceAsString(e));
        }
    }

}
