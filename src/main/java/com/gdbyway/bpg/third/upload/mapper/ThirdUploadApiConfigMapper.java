package com.gdbyway.bpg.third.upload.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <p>第三方数据上传接口配置表mapper</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Repository
public interface ThirdUploadApiConfigMapper extends BaseMapper<ThirdUploadApiConfigPO> {

    /**
     * <p>获取当前时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @return java.util.Date
     */
    @Select("select sysdate from dual")
    Date getNowDate();

}
