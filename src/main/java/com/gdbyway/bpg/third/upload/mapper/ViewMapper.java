package com.gdbyway.bpg.third.upload.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>视图mapper</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Repository
public interface ViewMapper extends BaseMapper {

    /**
     * <p>根据更新时间查询视图数据</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param queryBeginTime
     * @param queryEndTime
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    @Select("select * from ${apiConfig.uploadViewName} where update_date between #{queryBeginTime} and #{queryEndTime}")
    List<Map<String, Object>> queryViewDataListByUpdateDate(ThirdUploadApiConfigPO apiConfig,
                                                            Date queryBeginTime,
                                                            Date queryEndTime);

    /**
     * <p>根据多个pid查询视图数据</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param pidList
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    @Select("<script>select * from ${apiConfig.uploadViewName} where pid in " +
            "<foreach collection=\"pidList\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<Map<String, Object>> queryViewDataListByPidList(@Param("apiConfig") ThirdUploadApiConfigPO apiConfig, @Param("pidList") List<String> pidList);

}
