package com.gdbyway.bpg.third.upload.model.bo;

import com.gdbyway.bpg.third.upload.model.enums.ThirdUploadResultStatusEnum;
import lombok.Data;

/**
 * <p>接口调用结果</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class ApiCallResult {

    /** 唯一id，加锁用 */
    private String id;

    /** 总上传条数 */
    private long uploadRecord;

    /** 成功条数 */
    private long successRecord;

    /** 失败条数 */
    private long failRecord;

    /** 失败描述 */
    private String failDesc;

    public ApiCallResult() {
    }

    public ApiCallResult(long uploadRecord, long successRecord, long failRecord, String failDesc) {
        this.uploadRecord = uploadRecord;
        this.successRecord = successRecord;
        this.failRecord = failRecord;
        this.failDesc = failDesc;
    }

    /**
     * <p>结果</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @return com.gdbyway.bpg.third.upload.model.enums.ThirdUploadResultStatusEnum
     */
    public ThirdUploadResultStatusEnum getResult() {
        if(uploadRecord == successRecord) {
            return ThirdUploadResultStatusEnum.ALL_SUCCESS;
        } else if(uploadRecord == failRecord) {
            return ThirdUploadResultStatusEnum.ALL_FAIL;
        } else {
            return ThirdUploadResultStatusEnum.PART_FAIL;
        }
    }

    /**
     * <p>是否成功</p>
     * <AUTHOR>
     * @date 2024/11/15
     * @return java.lang.Boolean
     */
    public Boolean isSuccess() {
        return ThirdUploadResultStatusEnum.ALL_SUCCESS.equals(getResult());
    }

    /**
     * <p>合并结果</p>
     * <AUTHOR>
     * @date 2024/12/4
     * @param anotherApiCallResult
     */
    public void mergeApiCallResult(ApiCallResult anotherApiCallResult) {
        this.successRecord += anotherApiCallResult.getSuccessRecord();
        this.failRecord += anotherApiCallResult.getFailRecord();
    }

}
