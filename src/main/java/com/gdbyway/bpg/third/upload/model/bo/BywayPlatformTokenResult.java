package com.gdbyway.bpg.third.upload.model.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.Data;

@Data
@JacksonXmlRootElement(localName="result")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BywayPlatformTokenResult {

    @JacksonXmlProperty(localName = "getToken", isAttribute=true)
    private String getToken;

    @JacksonXmlText
    private String result;

}