package com.gdbyway.bpg.third.upload.model.command;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@JacksonXmlRootElement(localName = "message")
@AllArgsConstructor
public class BywayPlatformTokenCommand {
    @JacksonXmlProperty(localName = "epKey")
    private String epKey;
}