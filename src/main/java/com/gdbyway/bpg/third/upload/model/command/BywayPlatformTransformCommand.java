package com.gdbyway.bpg.third.upload.model.command;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlCData;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

/**
 * <p>TODO</p>
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@JacksonXmlRootElement(localName = "message")
public class BywayPlatformTransformCommand {

    @JacksonXmlProperty(localName = "key")
    private String key;

    @JacksonXmlCData
    @JacksonXmlProperty(localName = "msgContent")
    private String msgContent;

    @JacksonXmlProperty(localName = "msgCode")
    private String msgCode;

    @JacksonXmlProperty(localName = "msgVersion")
    private String msgVersion;

}