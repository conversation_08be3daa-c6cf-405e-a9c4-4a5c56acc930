package com.gdbyway.bpg.third.upload.model.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>上传任务</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class ThirdUploadJobCommand {

    /** 接口编码，对应sic_third_upload_api_config.api_code，是第三方文档的接口编码 */
    @JsonProperty("api_code")
    private String apiCode;

    /** 业务id数组，用于手动重推，非手动执行不填 */
    @JsonProperty("biz_id_array")
    private List<String> bizIdArray;

    /** 是否重推失败记录,用于手动重推失败记录，非手动执行不填，不建议和biz_id_array同时维护，否则优先按biz_id_array的配置为准 */
    @JsonProperty("is_retry_fail_record")
    private Boolean isRetryFailRecord;

    /** 查询分钟数
     * 用于设置查询结束时间，查询结束时间 = 查询开始时间 + 查询分钟数。
     * 用于避免单次上传数据量过大导致负载过重。
     * 一般定时任务频率高可以不设置该参数，不设置的话结束时间默认当前时间。
     */
    @JsonProperty("query_minutes")
    private Integer queryMinutes;

}
