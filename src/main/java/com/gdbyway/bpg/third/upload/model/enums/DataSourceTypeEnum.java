package com.gdbyway.bpg.third.upload.model.enums;

/**
 * <p>数据源类型枚举</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public enum DataSourceTypeEnum {

    /** oracle */
    ORACLE("jdbc:oracle:thin:@<<host>>:<<port>>/<<instance>>", "oracle.jdbc.OracleDriver"),

    /** mysql */
    MYSQL("jdbc:mysql://<<host>>:<<port>>/<<instance>>?Unicode=true&characterEncoding=UTF-8", "com.mysql.jdbc.Driver"),

    /** sql server */
    SQL_SERVER("jdbc:sqlserver://<<host>>:<<port>>;DatabaseName=<<instance>>", "com.microsoft.sqlserver.jdbc.SQLServerDriver");

    private String jdbcUrlTemplate;

    private String driver;

    public String getJdbcUrlTemplate() {
        return jdbcUrlTemplate;
    }

    public String getDriver() {
        return driver;
    }

    DataSourceTypeEnum(String jdbcUrlTemplate, String driver) {
        this.jdbcUrlTemplate = jdbcUrlTemplate;
        this.driver = driver;
    }
}
