package com.gdbyway.bpg.third.upload.model.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.gdbyway.bpg.third.upload.model.tl.JobExecuteUser;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * <p>po基类</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class BasePO {

    /** 主键 */
    @TableId
    private String pid;

    /** 创建人id */
    protected String createUserId;

    /** 创建人名称 */
    protected String createUserName;

    /** 创建日期 */
    protected Date createDate;

    /** 更新人id */
    protected String updateUserId;

    /** 更新人名称 */
    protected String updateUserName;

    /** 更新日期 */
    protected Date updateDate;

    /**
     * <p>创建</p>
     * <AUTHOR>
     * @date 2024/11/13
     */
    public void onSave() {
        this.pid = UUID.randomUUID().toString();
        this.createUserId = JobExecuteUser.get();
        this.createUserName = JobExecuteUser.get();
        this.updateUserId = JobExecuteUser.get();
        this.updateUserName = JobExecuteUser.get();
        this.createDate = JobStartDate.get();
        this.updateDate = JobStartDate.get();
    }

    /**
     * <p>更新</p>
     * <AUTHOR>
     * @date 2024/11/14
     */
    public void onUpdate() {
        this.updateUserId = JobExecuteUser.get();
        this.updateUserName = JobExecuteUser.get();
        this.updateDate = JobStartDate.get();
    }

}
