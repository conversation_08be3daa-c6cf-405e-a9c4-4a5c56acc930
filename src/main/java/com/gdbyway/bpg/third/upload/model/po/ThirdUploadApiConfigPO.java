package com.gdbyway.bpg.third.upload.model.po;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
/**
 * 
 * Copyright (C) 2024 Byway
 *
 * @description: SicThirdUploadApiConfig 数据传输对象
 * 
 * @version:v1.0.0 
 * @author: Vincent
 * 
 * Modification History:
 * Date         Author      Version     Description
 * -----------------------------------------------------------------
 *  2024-11-13     Vincent       v1.0.0        create
 *
 *
 */
@Data
@TableName("sic_third_upload_api_config")
public class ThirdUploadApiConfigPO extends BasePO {
	
	/** 接口编号，一般对应第三方接口文档上的接口编号 */
	private String apiCode;
	
	/** 接口名 */
	private String apiName;
	
	/** 接口调用方式（GET,POST,PUT,PATCH,DELETE） */
	private String callMethod;
	
	/** 接口地址（完整的接口地址） */
	private String apiUrl;

	/** 入参类型（JSON,XML） */
	private String inputType;
	
	/** xml根节点（当入参类型为xml时需配置） */
	private String xmlRoot;
	
	/** 最大失败次数 */
	private Integer maxFailCount;
	
	/** 是否支持批量上传（Y：支持，N：不支持。当设置为支持时，上传接口会多条数据通过数组形式上传。） */
	private String isBatchUpload;
	
	/** 接口成功校验字段 */
	private String successValidField;

	/** 接口成功校验字段值 */
	private String successValidValue;
	
	/** 上传视图名 */
	private String uploadViewName;

	/** 数据源编码，非本地数据库需配置 */
	private String dataSourceCode;

	/** 视图字段格式转换，不配置的话不做转换 UPPER：转换大写，LOWER：转换小写，UNDERLINE_UPPER：下划线且大写，UNDERLINE_LOWER：下划线且小写，CAMEL_CASE：驼峰命名 */
	private String fieldFormat;

	/** 上一次上传开始时间 */
	private Date lastUploadBeginTime;
	
	/** 上一次上传结束时间 */
	private Date lastUploadEndTime;
	
	/** 下一次上传查询开始时间 */
	private Date nextUploadQueryTime;
	
	/** 备注 */
	private String description;

	/** 是否可用 */
	private String isAvailable;

	/** 当所有记录上传报错时是否更新时间 Y-更新，N-不更新，缺省为N */
	private String updateTimeIfAllError;

	/** 是否平台接口 */
	private String isPlatformApi;

	/** 平台接口msgCode */
	private String platformMsgCode;
}

