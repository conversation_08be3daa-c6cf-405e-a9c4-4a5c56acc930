package com.gdbyway.bpg.third.upload.model.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gdbyway.bpg.third.upload.model.enums.ThirdUploadResultStatusEnum;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import lombok.Data;

import java.util.Date;

/**
 * 
 * Copyright (C) 2024 广东百慧科技有限公司
 *
 * <p>ThirdUploadDsConfig 持久化对象</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @data  2024-11-13
 * <p>
 * Modification History:
 * Date         Author      Version     Description
 * -----------------------------------------------------------------
 *  2024-11-13     VincentHo       v1.0.0        create
 */
@Data
@TableName("sic_third_upload_ds_config")
public class ThirdUploadDsConfigPO implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;

	@TableId
	private String pid;

	/** 数据源编号，自定义，不重复即可 */
	private String dataSourceCode;

	/** 数据源类型，ORACLE,MYSQL,SQL_SERVER */
	private String dataSourceType;

	/** 数据源主机名（ip） */
	private String dataSourceHost;

	/** 数据源端口号 */
	private String dataSourcePort;

	/** 数据源实例名 */
	private String dataSourceInstance;

	/** 数据源用户名 */
	private String dataSourceUser;

	/** 数据源密码 */
	private String dataSourcePassword;

	/** 是否更新数据源 */
	private String isUpdate;

	/** 备注 */
	private String description;

	/** 是否可用 */
	private String isAvailable;

}

