package com.gdbyway.bpg.third.upload.model.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gdbyway.bpg.third.upload.model.enums.ErrorRecordStatusEnum;
import lombok.Data;
/**
 * 
 * Copyright (C) 2024 广东百慧科技有限公司
 *
 * <p>SilThirdUploadErrorRecord 持久化对象</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @data  2024-11-13
 * <p>
 * Modification History:
 * Date         Author      Version     Description
 * -----------------------------------------------------------------
 *  2024-11-13     VincentHo       v1.0.0        create
 */
@Data
@TableName("SIL_THIRD_UPLOAD_ERROR_RECORD")
public class ThirdUploadErrorRecordPO extends BasePO implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;

	/** 接口编号，一般对应第三方接口文档上的接口编号 */
	private String apiCode;
	
	/** 接口名 */
	private String apiName;
	
	/** 业务id */
	private String bizId;
	
	/** 上传推送日志id */
	private String lastLogId;
	
	/** 失败次数 */
	private Integer failCount;
	
	/** 失败信息 */
	private String failMsg;
	
	/** SUCCESS：成功，RETRY：重试，FAIL_PASS：失败跳过 */
	private String status;

	public ThirdUploadErrorRecordPO errorLog(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log, String bizId, String failMsg) {
		log(log);
		this.apiCode = apiConfig.getApiCode();
		this.apiName = apiConfig.getApiName();
		this.bizId = bizId;
		this.failCount = 1;
		this.failMsg = failMsg;
		this.status = ErrorRecordStatusEnum.RETRY.name();
		return this;
	}

	/**
	 * <p>再次失败</p>
	 * <AUTHOR>
	 * @date 2024/11/14
	 * @param apiConfig
	 * @param log
	 * @param failMsg
	 */
	public void failAgain(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log, String failMsg) {
		log(log);
		this.failCount++;
		this.failMsg = failMsg;
		if(this.failCount >= apiConfig.getMaxFailCount()) {
			this.status = ErrorRecordStatusEnum.FAIL_PASS.name();
		}
	}

	/**
	 * <p>成功</p>
	 * <AUTHOR>
	 * @date 2024/11/14
	 * @param apiConfig
	 * @param log
	 */
	public void success(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log) {
		this.lastLogId = log.getPid();
		this.status = ErrorRecordStatusEnum.SUCCESS.name();
	}

	/**
	 * <p>记录日志通用方法</p>
	 * <AUTHOR>
	 * @date 2024/11/14
	 * @param log
	 */
	private void log(ThirdUploadLogPO log) {
		this.lastLogId = log.getPid();
	}
}

