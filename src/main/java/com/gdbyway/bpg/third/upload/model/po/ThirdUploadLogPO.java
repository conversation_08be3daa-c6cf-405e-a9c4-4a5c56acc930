package com.gdbyway.bpg.third.upload.model.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gdbyway.bpg.third.upload.model.enums.ThirdUploadResultStatusEnum;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import lombok.Data;
import java.util.Date;
/**
 * 
 * Copyright (C) 2024 广东百慧科技有限公司
 *
 * <p>SilThirdUploadLog 持久化对象</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @data  2024-11-13
 * <p>
 * Modification History:
 * Date         Author      Version     Description
 * -----------------------------------------------------------------
 *  2024-11-13     VincentHo       v1.0.0        create
 */
@Data
@TableName("sil_third_upload_log")
public class ThirdUploadLogPO extends BasePO implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;

	/** 接口编号，一般对应第三方接口文档上的接口编号 */
	private String apiCode;
	
	/** 接口名 */
	private String apiName;
	
	/** 上传开始时间 */
	private Date uploadBeginTime;
	
	/** 上传结束时间 */
	private Date uploadEndTime;

	/** 查询开始时间，本次查询业务数据的查询开始时间条件值 */
	private Date queryBeginTime;

	/** 查询结束时间，本次查询业务数据的查询结束时间条件值 */
	private Date queryEndTime;
	
	/** RUNNING：正在执行，ALL_SUCCESS：全部成功，ALL_FAIL：全部失败，PART_FAIL：部分失败 */
	private String status;
	
	/** 上传总记录数 */
	private Long uploadRecord;
	
	/** 成功记录数 */
	private Long successRecord;
	
	/** 失败记录数 */
	private Long failRecord;
	
	/** 失败的详细信息 */
	private String failDesc;

	/**
	 * <p>创建日志</p>
	 * <AUTHOR>
	 * @date 2024/11/13
	 * @param apiConfig
	 */
	public ThirdUploadLogPO create(ThirdUploadApiConfigPO apiConfig, Date queryBeginTime, Date queryEndTime, Long uploadRecord) {
		this.apiCode = apiConfig.getApiCode();
		this.apiName = apiConfig.getApiName();
		this.status = ThirdUploadResultStatusEnum.RUNNING.name();
		this.uploadBeginTime = JobStartDate.get();
		this.queryBeginTime = queryBeginTime;
		this.queryEndTime = queryEndTime;
		this.uploadRecord = uploadRecord;
		return this;
	}

}

