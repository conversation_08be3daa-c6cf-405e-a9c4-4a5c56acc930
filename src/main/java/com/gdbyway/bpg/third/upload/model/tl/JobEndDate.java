package com.gdbyway.bpg.third.upload.model.tl;

import java.util.Date;

/**
 * <p>任务结束时间</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public class JobEndDate {

    private final static ThreadLocal<Date> JOB_END_DATE = new ThreadLocal();

    public static void set(Date jobEndDate) {
        JOB_END_DATE.set(jobEndDate);
    }

    public static Date get() {
        if(JOB_END_DATE.get() == null) {
            set(new Date());
        }
        return JOB_END_DATE.get();
    }

    public static void remove() {
        JOB_END_DATE.remove();
    }

}
