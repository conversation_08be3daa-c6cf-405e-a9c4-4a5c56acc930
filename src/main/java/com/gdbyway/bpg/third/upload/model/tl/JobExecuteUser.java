package com.gdbyway.bpg.third.upload.model.tl;

/**
 * <p>任务执行人</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public class JobExecuteUser {

    private final static ThreadLocal<String> JOB_EXECUTE_USER = new ThreadLocal();

    public static void set(String jobExecuteUser) {
        JOB_EXECUTE_USER.set(jobExecuteUser);
    }

    public static String get() {
        if(JOB_EXECUTE_USER.get() == null) {
            set("admin");
        }
        return JOB_EXECUTE_USER.get();
    }

    public static void remove() {
        JOB_EXECUTE_USER.remove();
    }

}
