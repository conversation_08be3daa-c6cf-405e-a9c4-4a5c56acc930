package com.gdbyway.bpg.third.upload.model.tl;

import java.util.Date;

/**
 * <p>任务开始时间</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public class JobStartDate {

    private final static ThreadLocal<Date> JOB_START_DATE = new ThreadLocal();

    public static void set(Date jobStartDate) {
        JOB_START_DATE.set(jobStartDate);
    }

    public static Date get() {
        if(JOB_START_DATE.get() == null) {
            set(new Date());
        }
        return JOB_START_DATE.get();
    }

    public static void remove() {
        JOB_START_DATE.remove();
    }

}
