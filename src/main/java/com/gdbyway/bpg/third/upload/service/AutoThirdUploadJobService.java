package com.gdbyway.bpg.third.upload.service;

import com.gdbyway.bpg.third.upload.constant.ThirdUploadConstant;
import com.gdbyway.bpg.third.upload.model.command.ThirdUploadJobCommand;
import com.gdbyway.bpg.third.upload.model.enums.ThirdUploadResultStatusEnum;
import com.gdbyway.bpg.third.upload.model.enums.YesNoEnum;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.model.tl.JobEndDate;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import com.gdbyway.bpg.third.upload.service.db.ThirdUploadApiConfigService;
import com.gdbyway.bpg.third.upload.service.db.ViewService;
import com.gdbyway.bpg.third.upload.service.interfaces.AbstractThirdUploadJobService;
import com.gdbyway.bpg.third.upload.utils.ViewDataUtils;
import com.google.common.base.Throwables;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.vip.vjtools.vjkit.time.DateFormatUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <p>自动上传任务业务service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service(value = ThirdUploadConstant.AUTO_THIRD_UPLOAD_SERVICE_NAME)
public class AutoThirdUploadJobService extends AbstractThirdUploadJobService {

    @Autowired
    private ThirdUploadApiConfigService thirdUploadApiConfigService;

    @Autowired
    private ViewService viewService;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${third.upload.log.global.update-time-if-all-error:N}")
    private String globalUpdateTimeIfAllError;

    /** redis锁key前缀 */
    private final static String REDIS_LOCK_KEY_PREFIX = "AUTO_UPLOAD::";

    @Override
    public ReturnT upload(ThirdUploadJobCommand command) {

        RLock lock = redissonClient.getLock(REDIS_LOCK_KEY_PREFIX + command.getApiCode());

        try {
            // 看门狗机制上锁
            lock.lock();
            try {
                return super.upload(command);
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            XxlJobLogger.log("任务失败，失败信息:{} ", Throwables.getStackTraceAsString(e));
            return ReturnT.FAIL;
        }

    }

    @Override
    protected List<Map<String, Object>> queryViewData(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command) {

        Date queryBeginTime = getQueryBeginTime(apiConfig);
        Date queryEndTime = getQueryEndTime(apiConfig, command);

        XxlJobLogger.log("下一批次的数据，开始时间:[{}]，结束时间:[{}]",
                DateFormatUtil.formatDate(DateFormatUtil.PATTERN_DEFAULT_ON_SECOND, queryBeginTime),
                DateFormatUtil.formatDate(DateFormatUtil.PATTERN_DEFAULT_ON_SECOND, queryEndTime));

        List<Map<String, Object>> nextBatchData = viewService.queryViewDataListByUpdateDate(apiConfig,
                queryBeginTime, queryEndTime);

        List<Map<String, Object>> retryData = viewService.queryRetryViewDataListByApiConfig(apiConfig);

        return deduplicateByPid(ListUtil.union(nextBatchData, retryData));

    }

    /**
     * <p>通过pid去重</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param dataList
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    private List<Map<String, Object>> deduplicateByPid(List<Map<String, Object>> dataList) {
        Set<String> seenPidSet = new HashSet<>();
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> data : dataList) {
            String pid = ViewDataUtils.getBizId(data);
            // 如果pid之前没有出现过，添加到结果集并记录
            if (pid != null && seenPidSet.add(pid)) {
                result.add(data);
            }
        }

        return result;
    }

    @Override
    protected void preProcessing() {
        // do nothing
    }

    @Override
    protected void postProcessing(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command, ThirdUploadLogPO log) {

        boolean updateNextQueryTime = true;

        if(ThirdUploadResultStatusEnum.ALL_FAIL.name().equals(log.getStatus())) {
            updateNextQueryTime = shouldUpdateNextQueryTimeIfAllError(apiConfig.getUpdateTimeIfAllError());
        }

        if(updateNextQueryTime) {
            // 更新配置表时间
            XxlJobLogger.log("6.上传结束，更新下一次推送的查询起始时间...");
            apiConfig.setLastUploadBeginTime(JobStartDate.get());
            apiConfig.setLastUploadEndTime(JobEndDate.get());
            apiConfig.setNextUploadQueryTime(getQueryEndTime(apiConfig, command));
            apiConfig.onUpdate();
            thirdUploadApiConfigService.saveOrUpdate(apiConfig);
            XxlJobLogger.log("更新下一次推送的查询起始时间成功！");
        } else {
            XxlJobLogger.log("6.当前记录上传全部失败，不更新下一次推送的查询起始时间。");
            XxlJobLogger.log("相关配置：全部失败时是否更新下一次时间：全局配置[{}]，单个接口配置：[{}]", globalUpdateTimeIfAllError, apiConfig.getUpdateTimeIfAllError());
        }

    }

    /**
     * <p>当全部上传失败时，是否要更新下一次查询时间</p>
     * <AUTHOR>
     * @date 2025/1/21
     * @param configUpdateTimeIfAllError
     * @return boolean
     */
    private boolean shouldUpdateNextQueryTimeIfAllError(String configUpdateTimeIfAllError) {
        if (!StringUtils.isEmpty(configUpdateTimeIfAllError)) {
            return YesNoEnum.Y.name().equals(configUpdateTimeIfAllError);
        } else if (!StringUtils.isEmpty(globalUpdateTimeIfAllError)) {
            return YesNoEnum.Y.name().equals(globalUpdateTimeIfAllError);
        }
        return false;
    }

}
