package com.gdbyway.bpg.third.upload.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.gdbyway.bpg.third.upload.model.bo.ApiCallResult;
import com.gdbyway.bpg.third.upload.model.command.BywayPlatformTransformCommand;
import com.gdbyway.bpg.third.upload.model.enums.ApiCallMethodEnum;
import com.gdbyway.bpg.third.upload.model.enums.ApiParamTypeEnum;
import com.gdbyway.bpg.third.upload.model.enums.FieldFormatEnum;
import com.gdbyway.bpg.third.upload.model.enums.YesNoEnum;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.service.db.ThirdUploadErrorRecordService;
import com.gdbyway.bpg.third.upload.service.format.IFieldFormatService;
import com.gdbyway.bpg.third.upload.utils.ViewDataUtils;
import com.gdbyway.bpg.third.upload.utils.JsonUtils;
import com.gdbyway.bpg.third.upload.utils.RestTemplateUtils;
import com.gdbyway.bpg.third.upload.utils.XmlUtils;
import com.google.common.base.Throwables;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>接口调用service</p>
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service
public class CallApiService {

    @Autowired
    private Map<String, IFieldFormatService> formatServiceMap;

    @Autowired
    private ThirdUploadErrorRecordService thirdUploadErrorRecordService;

    @Value("${third.upload.log.view-data.enable:false}")
    private Boolean viewDataLogEnabled;

    /**
     * <p>调用接口</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param log
     * @param viewDataList
     * @return com.gdbyway.bpg.third.upload.model.bo.ApiCallResult
     */
    public ApiCallResult callApi(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log, List<Map<String, Object>> viewDataList, String platformToken) {
        // 调用第三方接口
        long uploadRecord = viewDataList.size();
        long successRecord = 0L;
        AtomicLong failRecord = new AtomicLong(0L);
        List<String> failBizIdList = new ArrayList<>();

        for (Map<String, Object> data : viewDataList) {
            String bizId = ViewDataUtils.getBizId(data);

            XxlJobLogger.log("开始推送业务id为[{}]的记录...", bizId);

            // 视图数据转换接口入参
            String apiParam;
            try {
                apiParam = YesNoEnum.Y.name().equals(apiConfig.getIsBatchUpload()) ?
                        viewData2ApiParam(apiConfig, ListUtil.newArrayList(data), platformToken) : viewData2ApiParam(apiConfig, data, platformToken);
            } catch (Exception e) {
                // 写失败日志
                String failMsg = Throwables.getStackTraceAsString(e);
                XxlJobLogger.log("【失败】视图数据转换接口入参失败，请检查视图格式，本记录[{}]执行失败：{}", bizId, failMsg);
                errorLog(apiConfig, log, bizId, failMsg, failRecord, failBizIdList);
                continue;
            }

            // 打印入参
            if(viewDataLogEnabled) {
                String logParam = apiParam;
                if(apiParam.trim().startsWith("<")) {
                    logParam = apiParam.replaceAll("<", "lt;").replaceAll(">", "gt;");
                    logParam = "<pre><code class=\"xml\">\n" + logParam + "\n</code></pre>";
                }
                XxlJobLogger.log("入参：{}", logParam);
            }

            // 调用接口
            Map<String, Object> result;
            try {
                result = calling(apiConfig, apiParam);
            } catch (Exception e) {
                // 写失败日志
                String failMsg = Throwables.getStackTraceAsString(e);
                XxlJobLogger.log("【失败】接口调用失败，本记录[{}]执行失败：{}", bizId, failMsg);
                errorLog(apiConfig, log, bizId, failMsg, failRecord, failBizIdList);
                continue;
            }

            // 接口出参结果解析
            if(resultValidate(apiConfig, result)) {
                thirdUploadErrorRecordService.successLog(apiConfig, log, bizId);
                successRecord++;
                XxlJobLogger.log("【成功】业务id为[{}]的数据推送成功！", bizId);
            } else {
                // 写失败日志
                XxlJobLogger.log("【失败】接口返回结果为失败，本记录[{}]执行失败。", bizId);
                errorLog(apiConfig, log, bizId, result.toString(), failRecord, failBizIdList);
            }

        }
        return new ApiCallResult(uploadRecord, successRecord, failRecord.get(),
                !CollectionUtils.isEmpty(failBizIdList) ? "本次推送失败的业务记录对应的业务id：" + failBizIdList.toString() : null);
    }

    private void errorLog(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log,
                          String bizId, String failMsg, AtomicLong failRecord, List<String> failBizIdList) {
        // 写失败日志
        thirdUploadErrorRecordService.errorLog(apiConfig, log, bizId, failMsg);
        failBizIdList.add(bizId);
        failRecord.getAndIncrement();
    }

    /**
     * <p>接口调用</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param apiParam
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> calling(ThirdUploadApiConfigPO apiConfig, String apiParam) {
        Map<String, Object> result;
        if(ApiCallMethodEnum.POST.name().equalsIgnoreCase(apiConfig.getCallMethod())) {
            ApiParamTypeEnum apiParamType = ApiParamTypeEnum.valueOf(apiConfig.getInputType().toUpperCase());
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", apiParamType.getContentType());
            result = RestTemplateUtils.postForObject(apiConfig.getApiUrl(), headers, null, apiParam, new ParameterizedTypeReference<Map<String, Object>>() {});
            XxlJobLogger.log("接口调用结果：{}", result);
        } else {
            // TODO 其他调用方式暂不实现
            throw new UnsupportedOperationException("暂不支持POST以外的请求方式");
        }
        return result;
    }

    /**
     * <p>接口结果校验</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param result
     * @return boolean
     */
    private boolean resultValidate(ThirdUploadApiConfigPO apiConfig, Map<String, Object> result) {
        try {
            String resultValue = getResultValue(apiConfig, result);
            return apiConfig.getSuccessValidValue().equalsIgnoreCase(resultValue);
        } catch (Exception e) {
            throw new RuntimeException("出参解析异常，可能原因为第三方接口出参格式错误，或接口调用失败，详情看上面的返回内容↑", e);
        }
    }

    private String getResultValue(ThirdUploadApiConfigPO apiConfig, Map<String, Object> result) {
        if("Y".equals(apiConfig.getIsPlatformApi())) {
            String syncResultStr = result.get("syncResult").toString();
            String successValidField = apiConfig.getSuccessValidField();
            if("boolean".equalsIgnoreCase(successValidField)) {
                return "true".equalsIgnoreCase(syncResultStr) ? "true" : "false";
            }
            // 根据内容格式选择解析工具
            if(syncResultStr.trim().startsWith("<")) {
                return XmlUtils.getNodeValue(syncResultStr, successValidField);
            } else {
                return JsonUtils.getNodeValue(syncResultStr, successValidField);
            }
        }
        // 非平台API，直接从result中获取验证字段的值
        return result.get(apiConfig.getSuccessValidField()).toString();
    }

    /**
     * <p>视图数据转换接口入参</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param data
     * @return java.lang.String
     */
    private String viewData2ApiParam(ThirdUploadApiConfigPO apiConfig, Map<String, Object> data, String platformToken) throws JsonProcessingException {
        if(apiConfig.getFieldFormat() != null) {
            FieldFormatEnum fieldFormat = FieldFormatEnum.valueOf(apiConfig.getFieldFormat());
            data = formatServiceMap.get(fieldFormat.name()).format(data);
        }

        String inputStr;

        if("xml".equalsIgnoreCase(apiConfig.getInputType())) {
            inputStr = XmlUtils.mapToXml(data, apiConfig.getXmlRoot());
        } else {
            inputStr = JsonUtils.writeValueAsString(data);
        }

        if("Y".equals(apiConfig.getIsPlatformApi())) {
            BywayPlatformTransformCommand command = new BywayPlatformTransformCommand();
            command.setKey(platformToken);
            command.setMsgCode(apiConfig.getPlatformMsgCode());
            command.setMsgVersion("1");
            command.setMsgContent(inputStr);
            if("xml".equalsIgnoreCase(apiConfig.getInputType())) {
                return XmlUtils.writeValueAsString(command);
            } else {
                return JsonUtils.writeValueAsString(data);
            }
        } else {
            return inputStr;
        }

    }

    /**
     * <p>视图数据转换接口入参</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param dataList
     * @return java.lang.String
     */
    private String viewData2ApiParam(ThirdUploadApiConfigPO apiConfig, List<Map<String, Object>> dataList, String platformToken) throws JsonProcessingException {
        if(apiConfig.getFieldFormat() != null) {
            FieldFormatEnum fieldFormat = FieldFormatEnum.valueOf(apiConfig.getFieldFormat());

            for (int i = 0; i < dataList.size(); i++) {
                Map<String, Object> data = dataList.get(i);
                data = formatServiceMap.get(fieldFormat.name()).format(data);
                dataList.set(i, data);
            }

        }

        if("xml".equalsIgnoreCase(apiConfig.getInputType())) {
            // TODO 待完善
            return XmlUtils.listToXml(dataList, apiConfig.getXmlRoot(), "item");
        } else {
            return JsonUtils.writeValueAsString(dataList);
        }

    }

}
