package com.gdbyway.bpg.third.upload.service;

import com.gdbyway.bpg.third.upload.constant.ThirdUploadConstant;
import com.gdbyway.bpg.third.upload.model.command.ThirdUploadJobCommand;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.service.db.ViewService;
import com.gdbyway.bpg.third.upload.service.interfaces.AbstractThirdUploadJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>自动上传任务业务service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service(value = ThirdUploadConstant.MANUAL_THIRD_UPLOAD_SERVICE_NAME)
public class ManualThirdUploadJobService extends AbstractThirdUploadJobService {

    @Autowired
    private ViewService viewService;

    @Override
    protected List<Map<String, Object>> queryViewData(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command) {
        if(!CollectionUtils.isEmpty(command.getBizIdArray())) {
            // 入参传入了业务id，则通过业务id查询重推
            return viewService.queryViewDataByBizIds(apiConfig, command.getBizIdArray());
        } else if(Boolean.TRUE.equals(command.getIsRetryFailRecord())) {
            // 入参传入了重推失败记录，则重推失败记录
            return viewService.queryRetryViewDataListByApiConfig(apiConfig);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    protected void preProcessing() {

    }

    @Override
    protected void postProcessing(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command, ThirdUploadLogPO log) {

    }
}
