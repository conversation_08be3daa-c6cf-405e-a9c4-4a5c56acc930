package com.gdbyway.bpg.third.upload.service;

import com.gdbyway.bpg.third.upload.config.BywayPlatformConfig;
import com.gdbyway.bpg.third.upload.model.bo.BywayPlatformTokenResult;
import com.gdbyway.bpg.third.upload.model.command.BywayPlatformTokenCommand;
import com.gdbyway.bpg.third.upload.utils.RestTemplateUtils;
import com.gdbyway.bpg.third.upload.utils.XmlUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>TODO</p>
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
@RequiredArgsConstructor
public class PlatformApiService {

    private final BywayPlatformConfig bywayPlatformConfig;

    @SneakyThrows
    public String getToken() {
        XxlJobLogger.log("开始获取平台token...");
        BywayPlatformTokenCommand command = new BywayPlatformTokenCommand(bywayPlatformConfig.getEpKey());
        String inputXml = XmlUtils.writeValueAsString(command);
        XxlJobLogger.log("获取平台token入参：{}", inputXml);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/xml");
        BywayPlatformTokenResult tokenResult = RestTemplateUtils.postForObject(bywayPlatformConfig.getTokenUrl(), headers, null, inputXml, new ParameterizedTypeReference<BywayPlatformTokenResult>() {});
        XxlJobLogger.log("获取平台token结束，出参：{}", tokenResult);
        return Optional.ofNullable(tokenResult).map(BywayPlatformTokenResult::getResult).orElse("");
    }

}
