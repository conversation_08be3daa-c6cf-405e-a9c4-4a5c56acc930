package com.gdbyway.bpg.third.upload.service;

import com.gdbyway.bpg.third.upload.model.command.ThirdUploadJobCommand;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>TODO</p>
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    private final AutoThirdUploadJobService autoThirdUploadJobService;

    @PostMapping("")
    public ReturnT test(@RequestBody ThirdUploadJobCommand command) {
        return autoThirdUploadJobService.upload(command);
    }

}
