package com.gdbyway.bpg.third.upload.service.db;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.BasicDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.gdbyway.bpg.third.upload.model.enums.DataSourceTypeEnum;
import com.gdbyway.bpg.third.upload.model.enums.YesNoEnum;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadDsConfigPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <p>数据源service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class DataSourceService {

    /** 动态数据源 */
    @Autowired
    private DynamicRoutingDataSource dynamicRoutingDataSource;

    /** 数据源创建器 */
    @Autowired
    private BasicDataSourceCreator dataSourceCreator;

    @Autowired
    private ThirdUploadDsConfigService thirdUploadDsConfigService;

    /**
     * <p>切换动态数据源</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param switchDataSource
     * @return boolean
     */
    public boolean switchDynamicDataSource(String switchDataSource, String defaultDataSource) {

        if(StringUtils.isEmpty(switchDataSource)) {
            return false;
        }

        ThirdUploadDsConfigPO thirdUploadDsConfig = thirdUploadDsConfigService.findByDataSourceCode(switchDataSource);
        if(!dynamicRoutingDataSource.getDataSources().containsKey(switchDataSource)) {
            if(thirdUploadDsConfig == null) {
                // 当查不到数据源配置时，取入参的默认配置，默认配置也没提供的话则不切换
                if(!StringUtils.isEmpty(defaultDataSource)) {
                    return switchDynamicDataSource(defaultDataSource, null);
                } else {
                    throw new RuntimeException(String.format("数据源切换异常，请到sic_third_upload_ds_config里面检查" +
                                    "data_source_code为[%s]的配置是否正确。",
                            switchDataSource));
                }
            } else {
                createDataSource(thirdUploadDsConfig);
            }
        } else {
            if(YesNoEnum.Y.name().equals(thirdUploadDsConfig.getIsUpdate())) {
                updateDataSource(thirdUploadDsConfig);
            }
        }
        DynamicDataSourceContextHolder.push(switchDataSource);
        return true;
    }

    /**
     * <p>释放动态数据源</p>
     * <AUTHOR>
     * @date 2024/11/14
     */
    public void releaseDynamicDataSource() {
        DynamicDataSourceContextHolder.poll();
    }

    /**
     * <p>更新数据源</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param thirdUploadDsConfig
     */
    private void updateDataSource(ThirdUploadDsConfigPO thirdUploadDsConfig) {
        dynamicRoutingDataSource.removeDataSource(thirdUploadDsConfig.getDataSourceCode());
        createDataSource(thirdUploadDsConfig);
    }


    /**
     * <p>创建数据源</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param thirdUploadDsConfig
     */
    private DataSource createDataSource(ThirdUploadDsConfigPO thirdUploadDsConfig) {
        try {
            DataSourceProperty dataSourceProperty = new DataSourceProperty();
            dataSourceProperty.setPoolName(thirdUploadDsConfig.getDataSourceCode());
            dataSourceProperty.setUsername(thirdUploadDsConfig.getDataSourceUser());
            dataSourceProperty.setPassword(thirdUploadDsConfig.getDataSourcePassword());

            DataSourceTypeEnum dataSourceType;
            try {
                dataSourceType = DataSourceTypeEnum.valueOf(thirdUploadDsConfig.getDataSourceType());
            } catch (Exception e) {
                throw new RuntimeException(String.format("不支持数据源类型[%s]，现只支持这些数据类型：[%s]，请修改配置。",
                        thirdUploadDsConfig.getDataSourceType(),
                        Arrays.stream(DataSourceTypeEnum.values()).map(DataSourceTypeEnum::name).collect(Collectors.joining(","))
                ));
            }

            dataSourceProperty.setUrl(dataSourceType.getJdbcUrlTemplate()
                    .replaceAll("<<host>>", thirdUploadDsConfig.getDataSourceHost())
                    .replaceAll("<<port>>", thirdUploadDsConfig.getDataSourcePort())
                    .replaceAll("<<instance>>", thirdUploadDsConfig.getDataSourceInstance())
            );
            dataSourceProperty.setDriverClassName(dataSourceType.getDriver());

            //创建数据源并添加到系统中管理
            DataSource dataSource = dataSourceCreator.createDataSource(dataSourceProperty);
            dynamicRoutingDataSource.addDataSource(thirdUploadDsConfig.getDataSourceCode(), dataSource);

            return dataSource;
        } catch (Exception e) {
            throw new RuntimeException(String.format("创建数据源失败，请到sic_third_upload_ds_config里面检查" +
                            "data_source_code为[%s]的配置是否正确。",
                    thirdUploadDsConfig.getDataSourceCode()));
        }

    }

}
