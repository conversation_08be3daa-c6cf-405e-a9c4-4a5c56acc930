package com.gdbyway.bpg.third.upload.service.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gdbyway.bpg.third.upload.mapper.ThirdUploadApiConfigMapper;
import com.gdbyway.bpg.third.upload.model.enums.YesNoEnum;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import org.springframework.stereotype.Service;
import com.xxl.job.core.log.XxlJobLogger;
import java.util.Date;

/**
 * <p>第三方数据上传接口配置service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ThirdUploadApiConfigService extends ServiceImpl<ThirdUploadApiConfigMapper, ThirdUploadApiConfigPO> {

    /**
     * <p>根据接口编码查询</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiCode
     * @return com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO
     */
    public ThirdUploadApiConfigPO findByApiCode(String apiCode){

        ThirdUploadApiConfigPO apiConfig = this.lambdaQuery()
                .eq(ThirdUploadApiConfigPO::getApiCode, apiCode)
                .eq(ThirdUploadApiConfigPO::getIsAvailable, YesNoEnum.Y.name())
                .one();

        if(apiConfig == null) {
            throw new RuntimeException(String.format("接口编号[{}]的接口的还未做配置，" +
                            "请参考wiki进行配置：http://wiki.gdbyway.com/pages/viewpage.action?pageId=156272259", apiCode));
        }

        XxlJobLogger.log("接口编号[{}]的接口的配置：{}", apiCode, apiConfig);

        return apiConfig;
    }

    /**
     * <p>获取当前时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @return java.util.Date
     */
    public Date getNowDate() {
        return getBaseMapper().getNowDate();
    }

}
