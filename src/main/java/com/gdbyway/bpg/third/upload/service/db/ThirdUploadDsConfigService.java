package com.gdbyway.bpg.third.upload.service.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gdbyway.bpg.third.upload.mapper.ThirdUploadDsConfigMapper;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadDsConfigPO;
import org.springframework.stereotype.Service;

/**
 * <p>第三方数据上传数据源配置表service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ThirdUploadDsConfigService extends ServiceImpl<ThirdUploadDsConfigMapper, ThirdUploadDsConfigPO> {

    /**
     * <p>通过编码查询</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param dataSourceCode
     * @return com.gdbyway.bpg.third.upload.model.po.ThirdUploadDsConfigPO
     */
    public ThirdUploadDsConfigPO findByDataSourceCode(String dataSourceCode) {
        return this.lambdaQuery().eq(ThirdUploadDsConfigPO::getDataSourceCode, dataSourceCode).one();
    }

}
