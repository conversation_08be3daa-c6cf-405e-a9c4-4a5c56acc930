package com.gdbyway.bpg.third.upload.service.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gdbyway.bpg.third.upload.mapper.ThirdUploadErrorRecordMapper;
import com.gdbyway.bpg.third.upload.mapper.ThirdUploadLogMapper;
import com.gdbyway.bpg.third.upload.model.enums.ErrorRecordStatusEnum;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadErrorRecordPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.model.tl.JobExecuteUser;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>第三方数据上传错误记录表service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ThirdUploadErrorRecordService extends ServiceImpl<ThirdUploadErrorRecordMapper, ThirdUploadErrorRecordPO> {

    /**
     * <p>根据接口编码查询重试的失败记录</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiCode
     * @return java.util.List<com.gdbyway.bpg.third.upload.model.po.ThirdUploadErrorRecordPO>
     */
    public List<ThirdUploadErrorRecordPO> findRetryRecordByApiCode(String apiCode) {
        return this.lambdaQuery()
                .eq(ThirdUploadErrorRecordPO::getApiCode, apiCode)
                .eq(ThirdUploadErrorRecordPO::getStatus, ErrorRecordStatusEnum.RETRY.name())
                .list();
    }

    /**
     * <p>根据接口编码，业务id和状态查询</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiCode
     * @param bizId
     * @param status
     * @return java.util.List<com.gdbyway.bpg.third.upload.model.po.ThirdUploadErrorRecordPO>
     */
    public List<ThirdUploadErrorRecordPO> findByApiCodeAndBizIdAndStatus(String apiCode, String bizId, String status) {
        return this.lambdaQuery()
                .eq(ThirdUploadErrorRecordPO::getApiCode, apiCode)
                .eq(ThirdUploadErrorRecordPO::getBizId, bizId)
                .eq(ThirdUploadErrorRecordPO::getStatus, status)
                .list();
    }

    /**
     * <p>记录错误日志</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param log
     * @param bizId
     * @param failMsg
     */
    @Transactional(rollbackFor = Exception.class)
    public void errorLog(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log, String bizId, String failMsg) {

        List<ThirdUploadErrorRecordPO> errorRecordList = findByApiCodeAndBizIdAndStatus(apiConfig.getApiCode(), bizId, ErrorRecordStatusEnum.RETRY.name());

        if(CollectionUtils.isEmpty(errorRecordList)) {
            ThirdUploadErrorRecordPO po = new ThirdUploadErrorRecordPO().errorLog(apiConfig, log, bizId, failMsg);
            po.onSave();
            this.save(po);
        } else {
            errorRecordList.forEach(record -> {
                record.failAgain(apiConfig, log, failMsg);
                record.onUpdate();
            });
            this.saveOrUpdateBatch(errorRecordList);
        }

    }

    /**
     * <p>成功日志</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param log
     * @param bizId
     */
    public void successLog(ThirdUploadApiConfigPO apiConfig, ThirdUploadLogPO log, String bizId) {
        this.lambdaUpdate()
                .eq(ThirdUploadErrorRecordPO::getApiCode, apiConfig.getApiCode())
                .eq(ThirdUploadErrorRecordPO::getBizId, bizId)
                .set(ThirdUploadErrorRecordPO::getStatus, ErrorRecordStatusEnum.SUCCESS.name())
                .set(ThirdUploadErrorRecordPO::getLastLogId, log.getPid())
                .set(ThirdUploadErrorRecordPO::getUpdateUserId, JobExecuteUser.get())
                .set(ThirdUploadErrorRecordPO::getUpdateUserName, JobExecuteUser.get())
                .set(ThirdUploadErrorRecordPO::getUpdateDate, JobStartDate.get())
                .update();
    }
}
