package com.gdbyway.bpg.third.upload.service.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gdbyway.bpg.third.upload.mapper.ThirdUploadLogMapper;
import com.gdbyway.bpg.third.upload.model.bo.ApiCallResult;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.model.tl.JobEndDate;
import org.springframework.stereotype.Service;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <p>第三方数据上传日志表service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ThirdUploadLogService extends ServiceImpl<ThirdUploadLogMapper, ThirdUploadLogPO> {

    /**
     * <p>创建日志</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param uploadRecord
     * @return com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO
     */
    @Transactional(rollbackFor = Exception.class)
    public ThirdUploadLogPO create(ThirdUploadApiConfigPO apiConfig, Date queryBeginTime, Date queryEndTime, Long uploadRecord) {
        ThirdUploadLogPO thirdUploadLog = new ThirdUploadLogPO().create(apiConfig, queryBeginTime, queryEndTime, uploadRecord);
        thirdUploadLog.onSave();
        this.saveOrUpdate(thirdUploadLog);
        return thirdUploadLog;
    }

    /**
     * <p>完成日志</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param thirdUploadLog
     * @param apiCallResult
     * @return com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO
     */
    @Transactional(rollbackFor = Exception.class)
    public ThirdUploadLogPO finishLog(ThirdUploadLogPO thirdUploadLog, ApiCallResult apiCallResult) {
        thirdUploadLog.setUploadEndTime(JobEndDate.get());
        thirdUploadLog.setStatus(apiCallResult.getResult().name());
        thirdUploadLog.setUploadRecord(apiCallResult.getUploadRecord());
        thirdUploadLog.setSuccessRecord(apiCallResult.getSuccessRecord());
        thirdUploadLog.setFailRecord(apiCallResult.getFailRecord());
        thirdUploadLog.setFailDesc(apiCallResult.getFailDesc());
        this.saveOrUpdate(thirdUploadLog);
        return thirdUploadLog;
    }
}
