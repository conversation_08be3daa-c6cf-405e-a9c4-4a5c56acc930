package com.gdbyway.bpg.third.upload.service.db;

import com.gdbyway.bpg.third.upload.constant.ThirdUploadConstant;
import com.gdbyway.bpg.third.upload.mapper.ViewMapper;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadErrorRecordPO;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>视图service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ViewService {

    @Autowired
    private ViewMapper viewMapper;

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private ThirdUploadErrorRecordService thirdUploadErrorRecordService;

    /**
     * <p>查询视图数据</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @param queryBeginTime
     * @param queryEndTime
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    public List<Map<String, Object>> queryViewDataListByUpdateDate(ThirdUploadApiConfigPO apiConfig,
                                                                   Date queryBeginTime,
                                                                   Date queryEndTime) {

        if(apiConfig == null) {
            return new ArrayList<>();
        }

        // 切换数据源
        boolean switchResult = dataSourceService.switchDynamicDataSource(apiConfig.getDataSourceCode(),
                ThirdUploadConstant.READ_DATA_SOURCE_NAME);
        try {
            return Optional
                    .ofNullable(viewMapper.queryViewDataListByUpdateDate(apiConfig, queryBeginTime, queryEndTime))
                    .orElse(new ArrayList<>());
        } finally {
            if(switchResult) {
                // 最后要释放掉切换的动态数据源
                dataSourceService.releaseDynamicDataSource();
            }
        }
    }

    /**
     * <p>通过api配置查询所有需要重推的视图数据</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param apiConfig
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    public List<Map<String, Object>> queryRetryViewDataListByApiConfig(ThirdUploadApiConfigPO apiConfig) {

        if(apiConfig == null) {
            return new ArrayList<>();
        }

        List<ThirdUploadErrorRecordPO> errorRecordList = thirdUploadErrorRecordService.findRetryRecordByApiCode(apiConfig.getApiCode());

        if(CollectionUtils.isEmpty(errorRecordList)) {
            return new ArrayList<>();
        }

        List<String> pidList = errorRecordList.stream().map(ThirdUploadErrorRecordPO::getBizId).collect(Collectors.toList());

        XxlJobLogger.log("重新推送之前失败的数据，重推以下业务id的数据：[{}]", pidList);

        // 切换数据源
        boolean switchResult = dataSourceService.switchDynamicDataSource(apiConfig.getDataSourceCode(),
                ThirdUploadConstant.READ_DATA_SOURCE_NAME);
        try {

            List<List<String>> pidListGroup = Lists.partition(pidList, 500);

            List<Map<String, Object>> resultList = new ArrayList<>();

            pidListGroup.forEach(list ->
                resultList.addAll(
                        Optional
                                .ofNullable(viewMapper.queryViewDataListByPidList(apiConfig, list))
                                .orElse(new ArrayList<>())
                )
            );

            return resultList;

        } finally {
            if(switchResult) {
                // 最后要释放掉切换的动态数据源
                dataSourceService.releaseDynamicDataSource();
            }
        }

    }

    /**
     * <p>根据业务id查询视图数据</p>
     * <AUTHOR>
     * @date 2024/11/15
     * @param apiConfig
     * @param bizIds
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    public List<Map<String, Object>> queryViewDataByBizIds(ThirdUploadApiConfigPO apiConfig, List<String> bizIds) {
        XxlJobLogger.log("推送业务id为{}的记录", bizIds);

        // 切换数据源
        boolean switchResult = dataSourceService.switchDynamicDataSource(apiConfig.getDataSourceCode(),
                ThirdUploadConstant.READ_DATA_SOURCE_NAME);
        try {
            return Optional
                    .ofNullable(viewMapper.queryViewDataListByPidList(apiConfig, bizIds))
                    .orElse(new ArrayList<>());
        } finally {
            if(switchResult) {
                // 最后要释放掉切换的动态数据源
                dataSourceService.releaseDynamicDataSource();
            }
        }

    }
}
