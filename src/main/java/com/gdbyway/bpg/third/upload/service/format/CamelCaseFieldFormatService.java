package com.gdbyway.bpg.third.upload.service.format;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>属性驼峰转换</p>
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service("CAMEL_CASE")
public class CamelCaseFieldFormatService implements IFieldFormatService {
    @Override
    public Map<String, Object> format(Map<String, Object> data) {
        if(data == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>(data.size());
        data.forEach((key, value) -> result.put(underlineToCamel(key), value));
        return result;
    }

    /**
     * <p>下划线转驼峰</p>
     * <AUTHOR>
     * @date 2024/11/15
     * @param str
     * @return java.lang.String
     */
    private String underlineToCamel(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        StringBuilder camelCaseString = new StringBuilder();
        boolean nextCharToUpper = false;

        for (char c : str.toLowerCase().toCharArray()) {
            if (c == '_') {
                nextCharToUpper = true;
            } else {
                if (nextCharToUpper) {
                    camelCaseString.append(Character.toUpperCase(c));
                    nextCharToUpper = false;
                } else {
                    camelCaseString.append(c);
                }
            }
        }

        return camelCaseString.toString();
    }

}
