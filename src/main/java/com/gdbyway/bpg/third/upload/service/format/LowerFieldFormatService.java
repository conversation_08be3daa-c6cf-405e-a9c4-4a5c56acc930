package com.gdbyway.bpg.third.upload.service.format;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>属性小写转换</p>
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service("LOWER")
public class LowerFieldFormatService implements IFieldFormatService {
    @Override
    public Map<String, Object> format(Map<String, Object> data) {
        if(data == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>(data.size());
        data.forEach((key, value) -> result.put(key.toLowerCase(), value));
        return result;
    }
}
