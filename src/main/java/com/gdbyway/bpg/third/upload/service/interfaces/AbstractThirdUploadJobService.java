package com.gdbyway.bpg.third.upload.service.interfaces;

import com.gdbyway.bpg.third.upload.model.bo.ApiCallResult;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadLogPO;
import com.gdbyway.bpg.third.upload.model.tl.JobEndDate;
import com.gdbyway.bpg.third.upload.model.tl.JobStartDate;
import com.gdbyway.bpg.third.upload.model.command.ThirdUploadJobCommand;
import com.gdbyway.bpg.third.upload.model.po.ThirdUploadApiConfigPO;
import com.gdbyway.bpg.third.upload.service.CallApiService;
import com.gdbyway.bpg.third.upload.service.PlatformApiService;
import com.gdbyway.bpg.third.upload.service.db.ThirdUploadApiConfigService;
import com.gdbyway.bpg.third.upload.service.db.ThirdUploadLogService;
import com.google.common.collect.Lists;
import com.vip.vjtools.vjkit.time.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobFileAppender;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <p>上传任务业务service</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public abstract class AbstractThirdUploadJobService {

    @Autowired
    protected ThirdUploadApiConfigService thirdUploadApiConfigService;

    @Autowired
    protected ThirdUploadLogService thirdUploadLogService;

    @Autowired
    protected CallApiService callApiService;

    @Autowired
    private ThreadPoolTaskExecutor commonAsyncThreadPoolExecutor;

    @Autowired
    private PlatformApiService platformApiService;

    @Value("${third.upload.push-whit-multithreading.enable:false}")
    private Boolean pushWithMultithreading;

    /**
     * <p>查询视图数据</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param command
     * @return List<Map<String, Object>>
     */
    protected abstract List<Map<String, Object>> queryViewData(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command);

    /**
     * <p>前置处理</p>
     * <AUTHOR>
     * @date 2024/11/13
     */
    protected abstract void preProcessing();

    /**
     * <p>后置处理</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param command
     * @param log
     */
    protected abstract void postProcessing(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand command, ThirdUploadLogPO log);

    /**
     * <p>上传</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param command
     * @return com.xxl.job.core.biz.model.ReturnT
     */
    public ReturnT upload(ThirdUploadJobCommand command) {

        XxlJobLogger.log("0.任务开始...");

        // 前置处理
        preProcessing();

        // 设置任务开始时间
        setupJobStartDate();

        // 根据接口编码查询配置
        ThirdUploadApiConfigPO apiConfig = thirdUploadApiConfigService.findByApiCode(command.getApiCode());

        // 查询视图数据
        XxlJobLogger.log("1.任务开始，现在开始查询视图{}的数据...", apiConfig.getUploadViewName());
        XxlJobLogger.log("本次推送数据如下：");
        List<Map<String, Object>> viewDataList = queryViewData(apiConfig, command);

        XxlJobLogger.log("【成功】本次查询到数据[{}]条，即将对这部分数据进行推送。", viewDataList.size());
        XxlJobLogger.log("2.现在推送开始...");

        // 创建日志
        XxlJobLogger.log("3.创建sil_third_upload_log日志记录...");
        ThirdUploadLogPO log = thirdUploadLogService.create(apiConfig, getQueryBeginTime(apiConfig), getQueryEndTime(apiConfig, command), Long.valueOf(viewDataList.size()));
        XxlJobLogger.log("【成功】sil_third_upload_log日志记录创建成功！");

        // 调用接口
        ApiCallResult apiCallResult = callApi(apiConfig, viewDataList, log);

        // 设置任务结束时间
        setupJobEndDate();

        // 完成日志
        XxlJobLogger.log("5.上传结束，更新结果到sil_third_upload_log日志记录...");
        log = thirdUploadLogService.finishLog(log, apiCallResult);
        XxlJobLogger.log("【成功】更新结果到sil_third_upload_log日志记录成功！");

        // 后置处理
        postProcessing(apiConfig, command, log);

        XxlJobLogger.log("本次任务结束！");
        XxlJobLogger.log("本次任务完成情况：");
        XxlJobLogger.log("总推送记录：[{}]，成功记录数：[{}]，失败记录数：[{}]",
                log.getUploadRecord(), log.getSuccessRecord(), log.getFailRecord());

        return apiCallResult.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    /**
     * <p>接口调用</p>
     * <AUTHOR>
     * @date 2024/12/4
     * @param apiConfig
     * @param viewDataList
     * @param log
     * @return com.gdbyway.bpg.third.upload.model.bo.ApiCallResult
     */
    private ApiCallResult callApi(ThirdUploadApiConfigPO apiConfig, List<Map<String, Object>> viewDataList, ThirdUploadLogPO log) {

        ApiCallResult apiCallResult = new ApiCallResult();
        apiCallResult.setId(UUID.randomUUID().toString());
        XxlJobLogger.log("4.解析视图数据并调用第三方接口上传...");
        if(!CollectionUtils.isEmpty(viewDataList)) {
            apiCallResult.setUploadRecord(viewDataList.size());

            String platformToken = getPlatformTokenIfPlatformApi(apiConfig);

            // 多线程处理，500条一个线程
            if(pushWithMultithreading) {
                List<List<Map<String, Object>>> partitionDataList = Lists.partition(viewDataList, 500);

                ThirdUploadLogPO finalLog = log;
                List<CompletableFuture<ApiCallResult>> futureList = new ArrayList<>();

                String mainThreadLogFile = XxlJobFileAppender.contextHolder.get();

                for (List<Map<String, Object>> dataList : partitionDataList) {
                    futureList.add(CompletableFuture
                            .supplyAsync(() -> {
                                XxlJobFileAppender.contextHolder.set(mainThreadLogFile);
                                return callApiService.callApi(apiConfig, finalLog, dataList, platformToken);
                            }, commonAsyncThreadPoolExecutor)
                    );
                }

                for (CompletableFuture<ApiCallResult> future : futureList) {
                    ApiCallResult batchResult = future.join();
                    this.mergeApiCallResult(apiCallResult, batchResult);
                }
            } else {
                apiCallResult = callApiService.callApi(apiConfig, log, viewDataList, platformToken);
            }

            XxlJobLogger.log("上传结束。");
        } else {
            XxlJobLogger.log("本次任务未查到业务数据，无需上传。");
        }

        return apiCallResult;

    }

    /**
     * 如果是平台 API则调用平台接口获取token
     *
     * @param apiConfig API 配置
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/05/20
     */
    private String getPlatformTokenIfPlatformApi(ThirdUploadApiConfigPO apiConfig) {

        if("Y".equals(apiConfig.getIsPlatformApi())) {
            String token = platformApiService.getToken();
            Assert.isTrue(!StringUtils.isEmpty(token), "未成功获取到平台的token，平台返回token为空");
            return token;
        }

        return null;
    }

    /**
     * <p>合并接口调用结果</p>
     * <AUTHOR>
     * @date 2024/12/4
     * @param finalResult
     * @param batchResult
     */
    private void mergeApiCallResult(ApiCallResult finalResult, ApiCallResult batchResult) {
        synchronized (finalResult.getId().intern()) {
            finalResult.mergeApiCallResult(batchResult);
        }
    }

    /**
     * <p>设置任务结束时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @return java.util.Date
     */
    protected Date setupJobEndDate() {
        // 获取数据库服务器当前时间
        Date nowDate = thirdUploadApiConfigService.getNowDate();
        JobEndDate.set(nowDate);
        return nowDate;
    }

    /**
     * <p>获取数据查询开始时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @return java.util.Date
     */
    protected Date getQueryBeginTime(ThirdUploadApiConfigPO apiConfig) {
        if(apiConfig == null) {
            return null;
        }

        return DateUtil.addMinutes(apiConfig.getNextUploadQueryTime(), -1);

    }

    /**
     * <p>获取数据查询结束时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param apiConfig
     * @param thirdUploadJobCommand
     * @return java.util.Date
     */
    protected Date getQueryEndTime(ThirdUploadApiConfigPO apiConfig, ThirdUploadJobCommand thirdUploadJobCommand) {
        Integer queryMinutes = thirdUploadJobCommand.getQueryMinutes();
        if(queryMinutes != null && apiConfig != null) {
            return DateUtil.addMinutes(apiConfig.getNextUploadQueryTime(), queryMinutes);
        }
        return JobStartDate.get();
    }

    /**
     * <p>设置任务开始时间</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @return java.util.Date
     */
    protected Date setupJobStartDate() {
        // 获取数据库服务器当前时间
        Date nowDate = thirdUploadApiConfigService.getNowDate();
        JobStartDate.set(nowDate);
        return nowDate;
    }

}
