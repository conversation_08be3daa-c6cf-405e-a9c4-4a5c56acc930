package com.gdbyway.bpg.third.upload.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <p>json工具类</p>
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public class JsonUtils {

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * <p>json串转换对象</p>
     * <AUTHOR>
     * @date 2024/11/13
     * @param jsonStr
     * @param type
     * @return T
     */
    public static <T> T readValue(String jsonStr, Class<T> type) throws JsonProcessingException {
        return objectMapper.readValue(jsonStr, type);
    }

    /**
     * <p>对象转换为json串</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param obj
     * @return java.lang.String
     */
    public static String writeValueAsString(Object obj) throws JsonProcessingException {
        return objectMapper.writeValueAsString(obj);
    }

    /**
     * <p>获取JSON中指定节点的值</p>
     * <AUTHOR>
     * @date 2024/11/15
     * @param jsonStr JSON字符串
     * @param nodeName 节点名称
     * @return java.lang.String 节点值，如果节点不存在返回null
     */
    public static String getNodeValue(String jsonStr, String nodeName) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonStr);
            JsonNode targetNode = rootNode.findValue(nodeName);
            return targetNode != null ? targetNode.asText() : null;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON解析失败", e);
        }
    }

}
