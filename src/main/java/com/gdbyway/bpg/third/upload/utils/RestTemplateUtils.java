package com.gdbyway.bpg.third.upload.utils;

import com.gdbyway.bpg.third.upload.config.RestTemplateConfig;
import lombok.SneakyThrows;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;

/**
 * <p>RestTemplate工具类</p>
 * <AUTHOR>
 * @date 2024/6/8
 */
public class RestTemplateUtils {

    private static RestTemplate restTemplate;

    /**
     * <p>获取RestTemplate</p>
     * <AUTHOR>
     * @date 2024/12/9
     * @return org.springframework.web.client.RestTemplate
     */
    public static RestTemplate getRestTemplate() {
        if (RestTemplateUtils.restTemplate == null) {
            synchronized (RestTemplateUtils.class) {
                if (RestTemplateUtils.restTemplate == null) {
                    RestTemplateUtils.restTemplate = createRestTemplate();
                }
            }
        }
        return RestTemplateUtils.restTemplate;
    }

    private static RestTemplate createRestTemplate(){
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 连接超时
        factory.setConnectTimeout(RestTemplateConfig.getConnectionTimeout());
        // 读取超时
        factory.setReadTimeout(RestTemplateConfig.getReadTimeout());
        return new RestTemplate(factory);
    }

    /**
     * 发送 GET 请求
     *
     * @param url     请求 URL
     * @param headers 请求头
     * @param params  请求参数对象
     * @param responseType 响应类型
     * @param <T>     响应类型泛型
     * @return 响应结果
     * @throws Exception 反序列化异常
     */
    @SneakyThrows
    public static <T> T getForObject(String url, HttpHeaders headers, Object params, ParameterizedTypeReference<T> responseType) {
        String urlWithParams = appendParamsToUrl(url, params);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = getRestTemplate().exchange(urlWithParams, HttpMethod.GET, null, responseType);
        if(!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new RuntimeException("调用接口[" + url + "]失败：\n" + response.getBody());
        }
        return response.getBody();
    }

    /**
     * 发送 POST 请求
     *
     * @param url     请求 URL
     * @param headers 请求头
     * @param params  请求参数对象
     * @param body    请求体
     * @param responseType 响应类型
     * @param <T>     响应类型泛型
     * @return 响应结果
     * @throws Exception 反序列化异常
     */
    @SneakyThrows
    public static <T> T postForObject(String url, HttpHeaders headers, Object params, Object body, ParameterizedTypeReference<T> responseType) {
        String urlWithParams = appendParamsToUrl(url, params);
        HttpEntity<Object> entity = new HttpEntity<>(body, headers);
        ResponseEntity<T> response = getRestTemplate().exchange(urlWithParams, HttpMethod.POST, entity, responseType);
        if(!HttpStatus.OK.equals(response.getStatusCode()) && !HttpStatus.CREATED.equals(response.getStatusCode())) {
            throw new RuntimeException("调用接口[" + url + "]失败：\n" + response.getBody());
        }
        return response.getBody();
    }

    /**
     * 将请求参数对象的属性拼接到 URL 后面
     *
     * @param url    请求 URL
     * @param params 请求参数对象
     * @return 拼接后的 URL
     * @throws Exception 获取参数属性值异常
     */
    private static String appendParamsToUrl(String url, Object params) throws Exception {
        if (params == null) {
            return url;
        }

        StringBuilder sb = new StringBuilder(url);
        if(url.contains("?")) {
            sb.append("&");
        } else {
            sb.append("?");
        }

        Field[] fields = params.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(params);
            if (value != null) {
                sb.append(field.getName()).append("=").append(value).append("&");
            }
        }

        return sb.toString();
    }
}