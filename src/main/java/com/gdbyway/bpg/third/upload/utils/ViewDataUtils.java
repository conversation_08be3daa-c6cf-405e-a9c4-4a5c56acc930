package com.gdbyway.bpg.third.upload.utils;

import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <p>视图数据工具类</p>
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public class ViewDataUtils {

    /**
     * <p>获取业务id</p>
     * <AUTHOR>
     * @date 2024/11/14
     * @param data
     * @return java.lang.String
     */
    public static String getBizId(Map<String, Object> data) {
        Object pidObj = Optional.ofNullable(data.get("pid"))
                .orElse(data.get("PID"));

        if(StringUtils.isEmpty(pidObj)) {
            throw new RuntimeException("未获取到pid，未能写入异常记录，本次推送失败。请修改视图，定义pid字段。" +
                    "详情参考wiki：http://wiki.gdbyway.com/pages/viewpage.action?pageId=156272259");
        }

        return pidObj.toString();

    }

}
