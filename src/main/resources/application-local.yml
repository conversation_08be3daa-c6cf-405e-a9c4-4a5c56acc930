server:
  port: 7105

spring:
  redis:
    database: 0
    host: ***************
    port: 6379
    password: ewell123
    timeout: 5s
    connect-timeout: 5s
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: *****************************************
          username: wsi
          password: uat_2022
          driver-class-name: oracle.jdbc.OracleDriver
        dg:
          url: *****************************************
          username: wsi
          password: uat_2022
          driver-class-name: oracle.jdbc.OracleDriver
third:
  upload:
    log:
      view-data:
        enable: true
      global:
        update-time-if-all-error: N
byway:
  job:
    accessToken: bx9v1fdp0lhdwmdzfgsnppt5nrktmr1egd6hxqf4b5zumjursdcm30w0bn8efh49-prod
    admin:
      addresses: http://localhost:8081/byway-job-admin
    executor:
      appname: ${spring.application.name}
      ip: ''
      logpath: /data/applogs/${spring.application.name}/
      logretentiondays: -1
      port: 0

#cat客户端配置
cat:
  client:
    config:
      domain: ${spring.application.name}
      serversMap:
        - ip: 127.0.0.1
          port: 2280
          httpPort: 8080
#apollo配置
#在本地开发模式下，Apollo只会从本地文件读取配置信息，不会从Apollo服务器读取配置
env: local
log:
  home:
    #${sys:log.home}  log4j2.xml 中使用
    /opt/app/third-upload-job-executor/log/

platform:
  ep-key: 8C99C98A-9B9E-EC06-3014-294416F56851
  token-url: http://uat-gdbyway-apps.gdbyway.com/api/esb-cluster/hie/srvmgr/hieSrvMgr/getToken