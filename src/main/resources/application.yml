server:
  servlet:
    context-path: /third-upload-job-executor.api
spring:
  # Application 的配置项
  application:
    name: third-upload-job-executor
  # Profile 的配置项
  profiles:
    active: local
  # SpringMVC 配置项
  mvc:
    throw-exception-if-no-handler-found: true # 匹配不到路径时，抛出 NoHandlerFoundException 异常
  main:
    allow-bean-definition-overriding: true
  resources:
    cache:
      period: 31536000
      cachecontrol:
        max-age: 31536000
    chain:
      html-application-cache: true
      enabled: true
      strategy:
        content:
          enabled: true
          paths: /webjars/swagger-ui/**.*
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# Actuator 监控配置项
management:
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'

web:
  debug:
    #启用DebugInterceptor
    enabled: true

