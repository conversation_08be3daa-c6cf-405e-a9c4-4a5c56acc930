<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration status="WARN" monitorInterval="30">
	<Properties>
		<!-- 配置日志文件输出目录，此配置将日志输出到tomcat根目录下的指定文件夹 -->
		<property name="LOG_HOME" value="${sys:LOG_PATH}"/>
		<!--服务模块名称-->
		<Property name="SERVICE_MODULE_NAME" value="third-upload-job-executor"/>
		<!-- 日志文件后带的时间 -->
		<property name="LOG_FILE_NAME_TIME" value="%d{yyyy_MM_dd}-%i"/>
		<!-- 分割文件大小 -->
		<Property name="FILE_SIZE">500MB</Property>
		<!-- 文件数量限制 -->
		<Property name="FILE_COUNT">4</Property>
		<!-- 20秒：PT20S，15分钟：PT15M，10小时：PT10H，2天：P2D -->
		<Property name="FILE_DELETE_AGE">P1D</Property>
		<!-- 日志格式：时间 日志级别 - 线程号 - 用户名 - 类路径:行号 - 信息 -->
		<property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss:SSS} - %-5p - %-3T - %X{TRACE_ID} - %X{USER} - %c{9.9.9.1}:%L - %m%n" />
		<property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss:SSS} %highlight{%-5p} - %t - %X{TRACE_ID} - %X{USER} - %c{9.9.9.9.9.1}:%L - %highlight{%m}%n" />
	</Properties>

	<!--先定义所有的appender-->
	<appenders>
		<!--这个输出控制台的配置-->
		<console name="Console" target="SYSTEM_OUT">
			<!--输出日志的格式-->
			<PatternLayout pattern="${CONSOLE_LOG_PATTERN}"/>
		</console>

		<!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，这个也挺有用的，适合临时测试用-->
		<File name="log" fileName="log/test.log" append="false">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
		</File>

		<!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
		<RollingFile name="RollingFileApp" fileName="${LOG_HOME}/${SERVICE_MODULE_NAME}-app.log"
					 filePattern="${LOG_HOME}/${SERVICE_MODULE_NAME}-app-${LOG_FILE_NAME_TIME}.log">
			<!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
			<ThresholdFilter level="error" onMatch="DENY" onMismatch="ACCEPT"/>
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${FILE_SIZE}"/>
			</Policies>
			<DefaultRolloverStrategy max="${FILE_COUNT}">
				<Delete basePath="${LOG_HOME}" maxDepth="3">
					<IfFileName glob="${SERVICE_MODULE_NAME}-app-*.log"/>
					<IfLastModified age="${FILE_DELETE_AGE}"/>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<!-- jdbc日志 -->
		<RollingFile name="RollingFileJdbc" fileName="${LOG_HOME}/${SERVICE_MODULE_NAME}-jdbc.log"
					 filePattern="${LOG_HOME}/${SERVICE_MODULE_NAME}-jdbc-${LOG_FILE_NAME_TIME}.log">
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${FILE_SIZE}"/>
			</Policies>
			<DefaultRolloverStrategy max="${FILE_COUNT}">
				<Delete basePath="${LOG_HOME}" maxDepth="3">
					<IfFileName glob="${SERVICE_MODULE_NAME}-jdbc-*.log"/>
					<IfLastModified age="${FILE_DELETE_AGE}"/>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<!-- 错误日志 -->
		<RollingFile name="RollingFileError" fileName="${LOG_HOME}/${SERVICE_MODULE_NAME}-error.log"
					 filePattern="${LOG_HOME}/${SERVICE_MODULE_NAME}-error-${LOG_FILE_NAME_TIME}.log">
			<ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${FILE_SIZE}"/>
			</Policies>
			<DefaultRolloverStrategy max="${FILE_COUNT}">
				<Delete basePath="${LOG_HOME}" maxDepth="3">
					<IfFileName glob="${SERVICE_MODULE_NAME}-error-*.log"/>
					<IfLastModified age="${FILE_DELETE_AGE}"/>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<CatAppender name="CatAppender"/>

	</appenders>

	<!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
	<loggers>

		<!--		<Logger name="org.hibernate" level="info" additivity="false">-->
		<!--			<appender-ref ref="Console"/>-->
		<!--			<appender-ref ref="RollingFileJdbc"/>-->
		<!--			<appender-ref ref="CatAppender"/>-->
		<!--		</Logger>-->

		<!-- 打印sql语句 -->
		<Logger name="org.hibernate.SQL" level="debug" additivity="false">
			<appender-ref ref="Console"/>
			<appender-ref ref="RollingFileJdbc"/>
			<appender-ref ref="CatAppender"/>
		</Logger>

		<!-- 打印sql语句的参数绑定 -->
		<Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="trace" additivity="false">
			<appender-ref ref="Console"/>
			<appender-ref ref="RollingFileJdbc"/>
			<appender-ref ref="CatAppender"/>
		</Logger>

		<!-- 查询出来的结果集提取 -->
		<Logger name="org.hibernate.type.descriptor.sql.BasicExtractor" level="off" additivity="false">
			<appender-ref ref="Console"/>
			<appender-ref ref="RollingFileJdbc"/>
			<appender-ref ref="CatAppender"/>
		</Logger>

		<root level="INFO">
			<appender-ref ref="Console"/>
			<appender-ref ref="RollingFileApp"/>
			<appender-ref ref="RollingFileError"/>
			<appender-ref ref="CatAppender"/>
		</root>>
	</loggers>
</configuration>
